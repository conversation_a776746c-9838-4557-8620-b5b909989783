<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend Message Sending</title>
</head>
<body>
    <h1>Test Frontend Message Sending</h1>
    
    <div>
        <h2>Set Authentication Token</h2>
        <input type="text" id="tokenInput" placeholder="Paste JWT token here" style="width: 500px;">
        <button onclick="setToken()">Set Token</button>
        <div id="tokenStatus"></div>
    </div>
    
    <div>
        <h2>Test Message Sending</h2>
        <input type="text" id="channelId" placeholder="Channel ID" value="68d18e977fbb0ae6ec30448b" style="width: 300px;">
        <br><br>
        <textarea id="messageText" placeholder="Message text" style="width: 400px; height: 100px;">Hello, this is a test message from frontend</textarea>
        <br><br>
        <button onclick="sendMessage()">Send Message</button>
        <div id="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://cds-dev.atomize.com.br';
        
        function setToken() {
            const token = document.getElementById('tokenInput').value;
            if (token) {
                localStorage.setItem('token', token);
                document.getElementById('tokenStatus').innerHTML = '<span style="color: green;">Token set successfully!</span>';
            } else {
                document.getElementById('tokenStatus').innerHTML = '<span style="color: red;">Please enter a token</span>';
            }
        }
        
        async function sendMessage() {
            const channelId = document.getElementById('channelId').value;
            const text = document.getElementById('messageText').value;
            const token = localStorage.getItem('token');
            
            if (!token) {
                document.getElementById('result').innerHTML = '<span style="color: red;">Please set authentication token first</span>';
                return;
            }
            
            if (!channelId || !text.trim()) {
                document.getElementById('result').innerHTML = '<span style="color: red;">Please provide both channel ID and message text</span>';
                return;
            }
            
            const payload = {
                channelId: channelId,
                messageType: 'text',
                text: text.trim()
            };
            
            console.log('Sending payload:', payload);
            
            try {
                const response = await fetch(`${API_BASE_URL}/communication/messages`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(payload)
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                const responseData = await response.text();
                console.log('Response body:', responseData);
                
                if (response.ok) {
                    document.getElementById('result').innerHTML = `
                        <div style="color: green;">
                            <h3>✅ Success!</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Response:</strong></p>
                            <pre>${responseData}</pre>
                        </div>
                    `;
                } else {
                    document.getElementById('result').innerHTML = `
                        <div style="color: red;">
                            <h3>❌ Error!</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Response:</strong></p>
                            <pre>${responseData}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Request failed:', error);
                document.getElementById('result').innerHTML = `
                    <div style="color: red;">
                        <h3>❌ Network Error!</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        // Pre-fill token if available
        const existingToken = localStorage.getItem('token');
        if (existingToken) {
            document.getElementById('tokenInput').value = existingToken;
            document.getElementById('tokenStatus').innerHTML = '<span style="color: green;">Token loaded from localStorage</span>';
        }
    </script>
</body>
</html>
