<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login and Message Sending</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        input, textarea, button { margin: 5px; padding: 8px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test Login and Message Sending</h1>
    
    <div class="section">
        <h2>1. Manual Token Setup</h2>
        <p>Use the provided token directly:</p>
        <button onclick="setProvidedToken()">Set Provided Token</button>
        <div id="tokenStatus"></div>
    </div>
    
    <div class="section">
        <h2>2. Get Channels</h2>
        <button onclick="getChannels()">Get Channels List</button>
        <div id="channelsResult"></div>
    </div>
    
    <div class="section">
        <h2>3. Send Message</h2>
        <input type="text" id="channelId" placeholder="Channel ID" style="width: 300px;">
        <br>
        <textarea id="messageText" placeholder="Message text" style="width: 400px; height: 100px;">Hello from frontend test!</textarea>
        <br>
        <button onclick="sendMessage()">Send Message</button>
        <div id="messageResult"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://cds-dev.atomize.com.br';
        const PROVIDED_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4YjYxNTAyYmY3N2M5ZTc2MmYzMmRhNiIsInJvbGUiOiJ0ZWFjaGVyIiwiaWF0IjoxNzU4NTQ2ODkyLCJleHAiOjE3NTg3MTk2OTJ9.7aRcj7SgGopnI-2Gu3zsrlswmjTi1NGenMEqllsQTaE';
        
        function setProvidedToken() {
            localStorage.setItem('token', PROVIDED_TOKEN);
            document.getElementById('tokenStatus').innerHTML = '<span class="success">✅ Token set successfully!</span>';
        }
        
        async function getChannels() {
            const token = localStorage.getItem('token');
            
            if (!token) {
                document.getElementById('channelsResult').innerHTML = '<span class="error">Please set token first</span>';
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/communication/channels`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.text();
                
                if (response.ok) {
                    const parsed = JSON.parse(data);
                    document.getElementById('channelsResult').innerHTML = `
                        <div class="success">
                            <h3>✅ Channels Retrieved!</h3>
                            <pre>${JSON.stringify(parsed, null, 2)}</pre>
                        </div>
                    `;
                    
                    // Auto-fill first channel ID if available
                    if (parsed.data && parsed.data.channels && parsed.data.channels.length > 0) {
                        document.getElementById('channelId').value = parsed.data.channels[0].channelId;
                    }
                } else {
                    document.getElementById('channelsResult').innerHTML = `
                        <div class="error">
                            <h3>❌ Error!</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <pre>${data}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('channelsResult').innerHTML = `
                    <div class="error">
                        <h3>❌ Network Error!</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function sendMessage() {
            const channelId = document.getElementById('channelId').value;
            const text = document.getElementById('messageText').value;
            const token = localStorage.getItem('token');
            
            if (!token) {
                document.getElementById('messageResult').innerHTML = '<span class="error">Please set token first</span>';
                return;
            }
            
            if (!channelId || !text.trim()) {
                document.getElementById('messageResult').innerHTML = '<span class="error">Please provide both channel ID and message text</span>';
                return;
            }
            
            const payload = {
                channelId: channelId,
                messageType: 'text',
                text: text.trim()
            };
            
            console.log('Sending payload:', payload);
            console.log('Using token:', token);
            
            try {
                const response = await fetch(`${API_BASE_URL}/communication/messages`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(payload)
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                const responseData = await response.text();
                console.log('Response body:', responseData);
                
                if (response.ok) {
                    document.getElementById('messageResult').innerHTML = `
                        <div class="success">
                            <h3>✅ Message Sent Successfully!</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <pre>${responseData}</pre>
                        </div>
                    `;
                } else {
                    document.getElementById('messageResult').innerHTML = `
                        <div class="error">
                            <h3>❌ Error Sending Message!</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <pre>${responseData}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Request failed:', error);
                document.getElementById('messageResult').innerHTML = `
                    <div class="error">
                        <h3>❌ Network Error!</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        // Auto-set token on load
        setProvidedToken();
    </script>
</body>
</html>
